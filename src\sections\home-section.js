/**
 * Home Section Component
 * Refactored to use the template configuration system
 */

import { BaseComponent } from '../core/template-factory.js';
import { createElement, createHtmlElement, renderTitle, renderSubTitle } from '../utils/utils.js';

export default class HomeSection extends BaseComponent {
  constructor(config, globalConfig) {
    super(config, globalConfig);
    this.personal = globalConfig.getPersonal();
    this.theme = globalConfig.getTheme();
    this.sectionConfig = config.config || {};
  }
  
  render() {
    const hero = createHtmlElement('section', {
      class: 'hero home__section',
      id: 'home'
    });
    
    const heroContent = this.createHeroContent();
    
    if (this.sectionConfig.showHeroImage !== false) {
      const heroImage = this.createHeroImage();
      hero.append(heroContent, heroImage);
    } else {
      hero.appendChild(heroContent);
    }
    
    this.element = hero;
    return hero;
  }
  
  /**
   * Create hero content section
   * @returns {HTMLElement} - Hero content element
   */
  createHeroContent() {
    const heroContent = createHtmlElement('div', { 
      class: 'hero__content' 
    });
    
    // Main title
    const mainHeader = createHtmlElement('h1', { 
      class: 'main__title' 
    });
    
    const titles = this.personal.heroTitles || [
      'Hello',
      `I'm ${this.personal.firstName || this.personal.name},`,
      this.personal.title.toLowerCase() + '.'
    ];
    
    renderTitle(titles, mainHeader);
    
    // Subtitle
    const subtitle = this.personal.subtitle || '';
    const paragraph = renderSubTitle(subtitle, 'sub__title');
    
    heroContent.append(mainHeader, paragraph);
    
    // Call-to-action button
    if (this.sectionConfig.showCTA !== false) {
      const ctaButton = this.createCTAButton();
      heroContent.appendChild(ctaButton);
    }
    
    return heroContent;
  }
  
  /**
   * Create call-to-action button
   * @returns {HTMLElement} - CTA button element
   */
  createCTAButton() {
    const ctaText = this.sectionConfig.ctaText || 'Get In Touch';
    const ctaAction = this.sectionConfig.ctaAction || 'contact';
    
    const button = createElement('button', {
      class: 'btn btn--hero',
      type: 'button',
      'aria-label': ctaText
    }, ctaText);
    
    // Apply theme styles
    this.applyThemeStyles(button, {
      backgroundColor: '$colors.primary',
      color: '$colors.text',
      borderRadius: '$borderRadius'
    });
    
    this.addEventListener(button, 'click', () => {
      document.dispatchEvent(new CustomEvent('portfolio:navigate', {
        detail: { 
          section: ctaAction, 
          path: ctaAction === 'home' ? '/' : `/${ctaAction}` 
        }
      }));
    });
    
    return button;
  }
  
  /**
   * Create hero image section
   * @returns {HTMLElement} - Hero image element
   */
  createHeroImage() {
    const heroImage = createHtmlElement('div', {
      class: 'hero__img hero__container'
    });
    
    // Background shape
    const bgShape = createHtmlElement('div', {
      class: 'hero__background-shape'
    });
    
    // Image container with animation
    const imageContainer = createHtmlElement('div', {
      class: 'image__container animate-profile'
    });
    
    const imgWrapper = createHtmlElement('div', {
      class: 'image__wrapper'
    });
    
    // Profile image
    if (this.personal.image) {
      const img = this.createOptimizedImage(
        this.personal.image, 
        `${this.personal.name} - Profile Picture`
      );
      imgWrapper.appendChild(img);
    }
    
    // Overlay for hover effects
    const overlay = createHtmlElement('div', {
      class: 'image__overlay'
    });
    
    imageContainer.append(imgWrapper, overlay);
    heroImage.append(bgShape, imageContainer);
    
    // Apply theme-based styling
    this.applyThemeStyles(bgShape, {
      background: `linear-gradient(135deg, ${this.theme.colors.primary}20, ${this.theme.colors.accent}20)`
    });
    
    return heroImage;
  }
  
  /**
   * Create optimized image element
   * @param {string} src - Image source
   * @param {string} alt - Alt text
   * @returns {HTMLImageElement} - Optimized image element
   */
  createOptimizedImage(src, alt) {
    const img = new Image();
    img.src = src;
    img.alt = alt;
    img.loading = 'lazy';
    img.decoding = 'async';
    
    // Add error handling
    this.addEventListener(img, 'error', () => {
      console.warn(`Failed to load image: ${src}`);
      img.style.display = 'none';
    });
    
    // Add load event for animations
    this.addEventListener(img, 'load', () => {
      img.classList.add('loaded');
    });
    
    return img;
  }
  
  /**
   * Update section with new configuration
   * @param {Object} newConfig - New configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.sectionConfig = this.config.config || {};
    
    // Re-render if element exists
    if (this.element) {
      const parent = this.element.parentNode;
      const newElement = this.render();
      if (parent) {
        parent.replaceChild(newElement, this.element);
      }
    }
  }
  
  /**
   * Get section data for external use
   * @returns {Object} - Section data
   */
  getSectionData() {
    return {
      name: 'home',
      title: this.personal.heroTitles?.[0] || 'Hello',
      subtitle: this.personal.subtitle,
      image: this.personal.image,
      cta: {
        text: this.sectionConfig.ctaText || 'Get In Touch',
        action: this.sectionConfig.ctaAction || 'contact'
      }
    };
  }
}

/**
 * Factory function for creating home section
 * @param {Object} config - Section configuration
 * @param {Object} globalConfig - Global configuration manager
 * @returns {HomeSection} - Home section instance
 */
export const createHomeSection = (config, globalConfig) => {
  return new HomeSection(config, globalConfig);
};

/**
 * Home section configuration schema
 */
export const HOME_SECTION_SCHEMA = {
  showHeroImage: {
    type: 'boolean',
    default: true,
    description: 'Whether to show the hero image'
  },
  showCTA: {
    type: 'boolean',
    default: true,
    description: 'Whether to show the call-to-action button'
  },
  ctaText: {
    type: 'string',
    default: 'Get In Touch',
    description: 'Text for the call-to-action button'
  },
  ctaAction: {
    type: 'string',
    default: 'contact',
    description: 'Section to navigate to when CTA is clicked'
  }
};

/**
 * Default home section configuration
 */
export const DEFAULT_HOME_CONFIG = {
  enabled: true,
  component: 'HomeSection',
  order: 1,
  config: {
    showHeroImage: true,
    showCTA: true,
    ctaText: 'Get In Touch',
    ctaAction: 'contact'
  }
};
