# Portfolio Template Configuration System

## Overview

The Portfolio Template Configuration System is a flexible, configuration-driven architecture that allows you to easily customize your portfolio without modifying the core code. This system follows functional programming principles and provides a clean separation between configuration and implementation.

## Key Features

- **Configuration-Driven**: Customize content, styling, and behavior through configuration files
- **Modular Architecture**: Each section is a self-contained component
- **Theme System**: Dynamic theming with CSS custom properties
- **Validation**: Built-in configuration validation and error handling
- **Extensible**: Easy to add custom sections and features
- **Type-Safe**: Schema validation for configuration objects
- **Performance**: Lazy loading and optimized rendering

## Quick Start

### 1. Basic Setup

```javascript
import { createPortfolioApp } from './core/portfolio-app.js';
import { USER_CONFIG } from './config/user.config.js';

// Initialize portfolio with your configuration
const app = await createPortfolioApp(USER_CONFIG);
```

### 2. Customize Your Portfolio

Edit `src/config/user.config.js` to personalize your portfolio:

```javascript
export const USER_CONFIG = {
  personal: {
    name: "Your Name",
    title: "Your Title",
    bio: "Your bio...",
    // ... more personal info
  },
  
  theme: {
    colors: {
      primary: "#your-color",
      // ... more colors
    }
  },
  
  // ... more configuration
};
```

## Configuration Structure

### Personal Information

```javascript
personal: {
  name: "Johnson Smith",           // Your full name
  firstName: "Johnson",            // First name for dynamic titles
  lastName: "Smith",               // Last name
  title: "Full Stack Developer",  // Professional title
  subtitle: "Frontend Specialist / React Expert", // Skills/roles
  bio: "Your professional bio...", // About section text
  email: "<EMAIL>",         // Contact email
  phone: "+****************",     // Phone number
  location: "City, State",         // Location
  image: "/images/your-photo.jpg", // Profile photo
  resume: "/assets/resume.pdf",    // Resume file
  brand: "<YourBrand />",          // Brand/logo text
  heroTitles: ["Hi!", "I'm You,", "developer."] // Home page titles
}
```

### Navigation Configuration

```javascript
navigation: [
  {
    text: "Home",        // Display text
    path: "/",           // URL path
    section: "home",     // Section ID
    isActive: true,      // Initially active
    enabled: true        // Show in navigation
  },
  // ... more nav items
]
```

### Section Configuration

```javascript
sections: {
  home: {
    enabled: true,              // Enable/disable section
    component: "HomeSection",   // Component to render
    order: 1,                   // Display order
    config: {                   // Section-specific config
      showHeroImage: true,
      showCTA: true,
      ctaText: "Get In Touch",
      ctaAction: "contact"
    }
  },
  // ... more sections
}
```

### Theme Configuration

```javascript
theme: {
  name: "custom",
  colors: {
    primary: "#6366f1",      // Primary brand color
    secondary: "#8b5cf6",    // Secondary color
    background: "#0f172a",   // Background color
    surface: "#1e293b",      // Card/surface color
    text: "#f8fafc",         // Primary text color
    textSecondary: "#cbd5e1", // Secondary text color
    accent: "#f59e0b",       // Accent color
    success: "#10b981",      // Success color
    warning: "#f59e0b",      // Warning color
    error: "#ef4444"         // Error color
  },
  fonts: {
    primary: "'Inter', sans-serif",
    secondary: "'JetBrains Mono', monospace",
    mono: "'Fira Code', monospace"
  },
  spacing: {
    xs: "0.25rem",
    sm: "0.5rem",
    md: "1rem",
    lg: "1.5rem",
    xl: "2rem",
    xxl: "3rem"
  },
  borderRadius: "12px",
  shadows: {
    sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
    md: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
    lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1)"
  }
}
```

## Advanced Usage

### Custom Sections

Create a custom section by extending the BaseComponent:

```javascript
import { BaseComponent } from '../core/template-factory.js';

export default class CustomSection extends BaseComponent {
  render() {
    const section = createElement('section', {
      class: 'hero custom-section',
      id: 'custom'
    });
    
    // Your custom content here
    
    return section;
  }
}
```

Register your custom section:

```javascript
// In your configuration
sections: {
  custom: {
    enabled: true,
    component: "CustomSection",
    order: 6,
    config: {
      // Custom section config
    }
  }
}
```

### Dynamic Theme Updates

```javascript
// Get the portfolio app instance
const app = await getPortfolioApp();

// Update theme at runtime
await app.updateConfig({
  theme: {
    colors: {
      primary: "#new-color"
    }
  }
});
```

### Configuration Validation

The system automatically validates your configuration:

```javascript
const configManager = createConfigManager(userConfig);
const validation = configManager.getValidation();

if (!validation.isValid) {
  console.error('Configuration errors:', validation.errors);
}
```

## API Reference

### ConfigManager

```javascript
const config = configManager.getConfig();           // Get full config
const personal = configManager.getPersonal();       // Get personal info
const navigation = configManager.getNavigation();   // Get navigation
const theme = configManager.getTheme();             // Get theme
const skills = configManager.getSkills();           // Get skills
const projects = configManager.getProjects();       // Get projects
```

### PortfolioApp

```javascript
const app = new PortfolioApp(userConfig);
await app.init();                    // Initialize app
await app.updateConfig(newConfig);   // Update configuration
app.getConfigManager();              // Get config manager
app.getTemplateFactory();            // Get template factory
app.isReady();                       // Check if initialized
app.destroy();                       // Clean up resources
```

## Best Practices

### 1. Configuration Organization

- Keep personal information in `personal` section
- Use meaningful section names and orders
- Group related theme properties together
- Validate configuration before deployment

### 2. Theme Design

- Use semantic color names (primary, secondary, etc.)
- Maintain consistent spacing scale
- Test theme in both light and dark environments
- Consider accessibility (contrast ratios)

### 3. Performance

- Optimize images before adding to configuration
- Use lazy loading for large content
- Minimize configuration file size
- Enable only needed features

### 4. Customization

- Start with default configuration and modify incrementally
- Test changes in development environment
- Keep backup of working configurations
- Document custom modifications

## Troubleshooting

### Common Issues

1. **Configuration Validation Errors**
   - Check required fields are present
   - Verify data types match schema
   - Ensure section components exist

2. **Theme Not Applied**
   - Check CSS custom properties are generated
   - Verify theme configuration structure
   - Clear browser cache

3. **Sections Not Rendering**
   - Ensure section is enabled
   - Check component name matches registered components
   - Verify section order is set

### Debug Mode

Enable debug mode in development:

```javascript
if (process.env.NODE_ENV === 'development') {
  window.portfolioApp = portfolioApp;
  window.configManager = portfolioApp.getConfigManager();
}
```

## Migration Guide

### From Legacy System

1. **Extract Configuration**: Move hardcoded values to configuration files
2. **Update Components**: Refactor components to use BaseComponent
3. **Theme Migration**: Convert CSS variables to theme configuration
4. **Test Thoroughly**: Verify all functionality works with new system

### Version Updates

- Check changelog for breaking changes
- Update configuration schema if needed
- Test custom components with new version
- Update documentation for custom modifications

## Examples

See the `src/config/user.config.js` file for complete examples including:
- `USER_CONFIG`: Full-featured configuration
- `MINIMAL_CONFIG`: Minimal setup
- `DESIGNER_CONFIG`: Design-focused configuration
