/**
 * Portfolio Application Entry Point
 * Updated to use the new template configuration system
 */

import { createPortfolioApp } from "./core/portfolio-app.js";
import { USER_CONFIG } from "./config/user.config.js";
import "./style/style.css";

/**
 * Initialize the portfolio application
 */
async function initializePortfolio() {
  try {
    // Create and initialize the portfolio app with user configuration
    const portfolioApp = await createPortfolioApp(USER_CONFIG);

    // Log successful initialization
    console.log("Portfolio initialized successfully!");

    // Optional: Expose app instance globally for debugging
    if (process.env.NODE_ENV === "development") {
      window.portfolioApp = portfolioApp;
    }

    return portfolioApp;
  } catch (error) {
    console.error("Failed to initialize portfolio:", error);

    // Fallback: Show error message to user
    const app = document.getElementById("app");
    if (app) {
      app.innerHTML = `
        <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100vh;
          text-align: center;
          padding: 2rem;
          color: #ef4444;
        ">
          <h1>Portfolio Loading Error</h1>
          <p>There was an error loading the portfolio. Please check the console for details.</p>
          <button onclick="location.reload()" style="
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          ">
            Retry
          </button>
        </div>
      `;
    }
  }
}

// Initialize when DOM is ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initializePortfolio);
} else {
  initializePortfolio();
}
