/**
 * User Configuration File
 * Customize your portfolio by modifying this configuration
 * This file will override the default configuration
 */

export const USER_CONFIG = {
  // Personal Information - Customize with your details
  personal: {
    name: "<PERSON>",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    title: "Full Stack Developer",
    subtitle: "Frontend Specialist / React Expert / JavaScript Enthusiast / UI/UX Designer",
    bio: "I'm a passionate Full Stack Developer with 5+ years of experience creating beautiful, functional web applications. I specialize in React, Node.js, and modern web technologies, with a keen eye for design and user experience.",
    email: "<EMAIL>",
    phone: "+****************",
    location: "San Francisco, CA",
    image: "/images/user.jpg",
    resume: "/assets/resume.pdf",
    brand: "<JohnsonDev />",
    heroTitles: ["Hi there!", "I'm <PERSON>,", "full stack developer."]
  },

  // Social Media Links - Update with your profiles
  social: {
    github: "https://github.com/johnsonsmith",
    linkedin: "https://linkedin.com/in/johnsonsmith",
    twitter: "https://twitter.com/johnsondev",
    email: "mailto:<EMAIL>",
    portfolio: "https://johnsonsmith.dev"
  },

  // Customize Navigation
  navigation: [
    { text: "Home", path: "/", section: "home", isActive: true, enabled: true },
    { text: "About", path: "/about", section: "about", enabled: true },
    { text: "Skills", path: "/skills", section: "skills", enabled: true },
    { text: "Projects", path: "/projects", section: "projects", enabled: true },
    { text: "Contact", path: "/contact", section: "contact", enabled: true }
  ],

  // Section Configuration - Enable/disable sections and customize behavior
  sections: {
    home: {
      enabled: true,
      component: "HomeSection",
      order: 1,
      config: {
        showHeroImage: true,
        showCTA: true,
        ctaText: "Let's Work Together",
        ctaAction: "contact"
      }
    },
    about: {
      enabled: true,
      component: "AboutSection", 
      order: 2,
      config: {
        showSkillCards: true,
        showResumeButton: true,
        resumeButtonText: "Download Resume"
      }
    },
    skills: {
      enabled: true,
      component: "SkillsSection",
      order: 3,
      config: {
        displayType: "animated", // "animated", "bars", "grid"
        showThemeSelector: true,
        enableInteraction: true
      }
    },
    projects: {
      enabled: true,
      component: "ProjectsSection",
      order: 4,
      config: {
        showModal: true,
        itemsPerRow: 3,
        showTechnologies: true,
        showGithubLinks: true
      }
    },
    contact: {
      enabled: true,
      component: "ContactSection",
      order: 5,
      config: {
        formEndpoint: "https://formspree.io/f/your-form-id", // Replace with your form endpoint
        showSocialIcons: true,
        enableValidation: true
      }
    }
  },

  // Custom Theme - Personalize colors and styling
  theme: {
    name: "custom",
    colors: {
      primary: "#6366f1",        // Indigo
      secondary: "#8b5cf6",      // Purple  
      background: "#0f172a",     // Dark slate
      surface: "#1e293b",        // Slate
      text: "#f8fafc",           // Light
      textSecondary: "#cbd5e1",  // Gray
      accent: "#f59e0b",         // Amber
      success: "#10b981",        // Emerald
      warning: "#f59e0b",        // Amber
      error: "#ef4444"           // Red
    },
    fonts: {
      primary: "'Inter', sans-serif",
      secondary: "'JetBrains Mono', monospace",
      mono: "'Fira Code', monospace"
    },
    spacing: {
      xs: "0.25rem",
      sm: "0.5rem", 
      md: "1rem",
      lg: "1.5rem",
      xl: "2rem",
      xxl: "3rem"
    },
    borderRadius: "12px",
    shadows: {
      sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
      md: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
      lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1)"
    }
  },

  // Skills - Add your technical skills
  skills: [
    { name: "JavaScript", level: 95, icon: "/images/icons/js.svg", category: "frontend" },
    { name: "TypeScript", level: 90, icon: "/images/icons/typescript.svg", category: "frontend" },
    { name: "React", level: 92, icon: "/images/icons/react.svg", category: "frontend" },
    { name: "Next.js", level: 88, icon: "/images/icons/nextjs.svg", category: "frontend" },
    { name: "Vue.js", level: 80, icon: "/images/icons/vuejs.svg", category: "frontend" },
    { name: "Node.js", level: 85, icon: "/images/icons/nodejs.svg", category: "backend" },
    { name: "Express", level: 83, icon: "/images/icons/express.svg", category: "backend" },
    { name: "MongoDB", level: 78, icon: "/images/icons/mongodb.svg", category: "database" },
    { name: "PostgreSQL", level: 82, icon: "/images/icons/postgresql.svg", category: "database" },
    { name: "Docker", level: 75, icon: "/images/icons/docker.svg", category: "devops" },
    { name: "AWS", level: 70, icon: "/images/icons/aws.svg", category: "devops" },
    { name: "Git", level: 90, icon: "/images/icons/git.svg", category: "tools" }
  ],

  // Projects - Showcase your work
  projects: [
    {
      id: "ecommerce-platform",
      title: "E-Commerce Platform",
      subtitle: "Full Stack Application",
      description: "A modern e-commerce platform built with React, Node.js, and MongoDB featuring real-time inventory management and payment processing.",
      longDescription: "A comprehensive e-commerce solution featuring user authentication, product catalog, shopping cart, payment integration with Stripe, order management, and admin dashboard. Built with modern technologies and best practices.",
      technologies: ["React", "Node.js", "MongoDB", "Express", "Stripe API", "JWT", "Redux"],
      image: "/images/projects/ecommerce.jpg",
      github: "https://github.com/johnsonsmith/ecommerce-platform",
      demo: "https://ecommerce-demo.johnsonsmith.dev",
      featured: true,
      category: "full-stack",
      status: "completed"
    },
    {
      id: "task-management-app",
      title: "Task Management App",
      subtitle: "React & Firebase",
      description: "A collaborative task management application with real-time updates, team collaboration features, and intuitive drag-and-drop interface.",
      longDescription: "A feature-rich task management application that allows teams to collaborate effectively. Includes real-time updates, file attachments, comments, time tracking, and comprehensive reporting features.",
      technologies: ["React", "Firebase", "Material-UI", "React DnD", "Chart.js"],
      image: "/images/projects/task-manager.jpg", 
      github: "https://github.com/johnsonsmith/task-manager",
      demo: "https://tasks.johnsonsmith.dev",
      featured: true,
      category: "web-app",
      status: "completed"
    },
    {
      id: "weather-dashboard",
      title: "Weather Dashboard",
      subtitle: "Vue.js & API Integration",
      description: "A responsive weather dashboard with location-based forecasts, interactive maps, and detailed weather analytics.",
      longDescription: "An elegant weather dashboard that provides comprehensive weather information including current conditions, 7-day forecasts, weather maps, and historical data visualization.",
      technologies: ["Vue.js", "Vuex", "OpenWeather API", "Mapbox", "Chart.js", "PWA"],
      image: "/images/projects/weather-dashboard.jpg",
      github: "https://github.com/johnsonsmith/weather-dashboard", 
      demo: "https://weather.johnsonsmith.dev",
      featured: false,
      category: "web-app",
      status: "completed"
    }
  ],

  // Animation Settings
  animations: {
    enabled: true,
    duration: 1000,
    easing: "ease-out-cubic",
    offset: 100,
    once: true,
    delay: 100
  },

  // SEO Configuration
  seo: {
    title: "Johnson Smith - Full Stack Developer",
    description: "Experienced Full Stack Developer specializing in React, Node.js, and modern web technologies. Creating beautiful, functional web applications.",
    keywords: ["full stack developer", "react developer", "node.js", "javascript", "web development", "frontend", "backend"],
    author: "Johnson Smith",
    image: "/images/og-image.jpg",
    url: "https://johnsonsmith.dev"
  },

  // Feature Flags - Enable/disable features
  features: {
    darkMode: true,
    animations: true,
    particleBackground: true,
    soundEffects: false,
    analytics: true,
    contactForm: true,
    blog: false,
    testimonials: false
  }
};

// Alternative configurations for different use cases
export const MINIMAL_CONFIG = {
  personal: {
    name: "Your Name",
    title: "Developer",
    bio: "A passionate developer creating amazing web experiences."
  },
  sections: {
    home: { enabled: true, component: "HomeSection", order: 1 },
    about: { enabled: true, component: "AboutSection", order: 2 },
    contact: { enabled: true, component: "ContactSection", order: 3 }
  },
  features: {
    animations: false,
    particleBackground: false
  }
};

export const DESIGNER_CONFIG = {
  theme: {
    colors: {
      primary: "#ff6b6b",
      secondary: "#4ecdc4", 
      accent: "#45b7d1"
    }
  },
  sections: {
    home: { enabled: true, component: "HomeSection", order: 1 },
    about: { enabled: true, component: "AboutSection", order: 2 },
    projects: { enabled: true, component: "ProjectsSection", order: 3 },
    contact: { enabled: true, component: "ContactSection", order: 4 }
  },
  features: {
    animations: true,
    particleBackground: true
  }
};
