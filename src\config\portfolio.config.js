/**
 * Portfolio Configuration Schema
 * This file defines the complete configuration structure for the portfolio template
 */

export const DEFAULT_PORTFOLIO_CONFIG = {
  // Personal Information
  personal: {
    name: "<PERSON>",
    firstName: "<PERSON>",
    lastName: "",
    title: "Web Developer",
    subtitle: "Front End Developer / JavaScript Expert / Freelancer / Teacher",
    bio: "I'm a passionate Frontend with three years of experience turning ideas into reality and still counting. The thrill of bringing a static design to life with code is what gets me going every morning.",
    email: "<EMAIL>",
    phone: "+****************",
    location: "New York, NY",
    image: "/images/user.jpg",
    resume: "/assets/resume.pdf",
    brand: "<Jayblacc />",
    heroTitles: ["Hello", "I'm <PERSON>,", "web developer."]
  },

  // Social Media Links
  social: {
    github: "https://github.com/jayblacc",
    linkedin: "https://linkedin.com/in/jayblacc",
    twitter: "https://twitter.com/jayblacc",
    email: "mailto:<EMAIL>",
    skype: "https://skype.com/jayblacc"
  },

  // Navigation Configuration
  navigation: [
    { 
      text: "Home", 
      path: "/", 
      section: "home", 
      isActive: true,
      enabled: true 
    },
    { 
      text: "About", 
      path: "/about", 
      section: "about", 
      isActive: false,
      enabled: true 
    },
    { 
      text: "Skills", 
      path: "/skills", 
      section: "skills", 
      isActive: false,
      enabled: true 
    },
    { 
      text: "Projects", 
      path: "/projects", 
      section: "projects", 
      isActive: false,
      enabled: true 
    },
    { 
      text: "Contact", 
      path: "/contact", 
      section: "contact", 
      isActive: false,
      enabled: true 
    }
  ],

  // Section Configuration
  sections: {
    home: {
      enabled: true,
      component: "HomeSection",
      order: 1,
      config: {
        showHeroImage: true,
        showCTA: true,
        ctaText: "Get In Touch",
        ctaAction: "contact"
      }
    },
    about: {
      enabled: true,
      component: "AboutSection",
      order: 2,
      config: {
        showSkillCards: true,
        showResumeButton: true,
        resumeButtonText: "Get Resume"
      }
    },
    skills: {
      enabled: true,
      component: "SkillsSection",
      order: 3,
      config: {
        displayType: "animated", // "animated", "bars", "grid"
        showThemeSelector: true,
        enableInteraction: true
      }
    },
    projects: {
      enabled: true,
      component: "ProjectsSection",
      order: 4,
      config: {
        showModal: true,
        itemsPerRow: 3,
        showTechnologies: true,
        showGithubLinks: true
      }
    },
    contact: {
      enabled: true,
      component: "ContactSection",
      order: 5,
      config: {
        formEndpoint: "https://formspree.io/f/mknyknkr",
        showSocialIcons: true,
        enableValidation: true
      }
    }
  },

  // Theme Configuration
  theme: {
    name: "default",
    colors: {
      primary: "#5462ffe4",
      secondary: "#858e99",
      background: "#1d1d1d",
      surface: "#2a2a2a",
      text: "#ffffff",
      textSecondary: "#b0b0b0",
      accent: "#ff6b6b",
      success: "#4caf50",
      warning: "#ff9800",
      error: "#f44336"
    },
    fonts: {
      primary: "'Poppins', sans-serif",
      secondary: "'Mitr', sans-serif",
      mono: "'Fira Code', monospace"
    },
    spacing: {
      xs: "0.25rem",
      sm: "0.5rem",
      md: "1rem",
      lg: "1.5rem",
      xl: "2rem",
      xxl: "3rem"
    },
    borderRadius: "8px",
    shadows: {
      sm: "0 2px 4px rgba(0,0,0,0.1)",
      md: "0 4px 8px rgba(0,0,0,0.15)",
      lg: "0 8px 16px rgba(0,0,0,0.2)"
    }
  },

  // Skills Configuration
  skills: [
    { name: "JavaScript", level: 90, icon: "/images/icons/js.svg", category: "frontend" },
    { name: "TypeScript", level: 85, icon: "/images/icons/typescript.svg", category: "frontend" },
    { name: "React", level: 88, icon: "/images/icons/react.svg", category: "frontend" },
    { name: "Vue.js", level: 75, icon: "/images/icons/vuejs.svg", category: "frontend" },
    { name: "HTML5", level: 95, icon: "/images/icons/html.svg", category: "frontend" },
    { name: "CSS3", level: 90, icon: "/images/icons/css.svg", category: "frontend" },
    { name: "Node.js", level: 80, icon: "/images/icons/nodejs.svg", category: "backend" },
    { name: "Git", level: 85, icon: "/images/icons/git.svg", category: "tools" },
    { name: "Webpack", level: 75, icon: "/images/icons/webpack.svg", category: "tools" },
    { name: "SASS", level: 85, icon: "/images/icons/sass.svg", category: "frontend" }
  ],

  // Projects Configuration
  projects: [
    {
      id: "recipe-app",
      title: "Recipe App",
      subtitle: "16 | 24",
      description: "Vanilla JS project with a focus on creating a visually appealing and interactive user interface.",
      longDescription: "A comprehensive recipe application built with vanilla JavaScript, featuring advanced search functionality, ingredient management, and responsive design. The app demonstrates modern web development practices without relying on frameworks.",
      technologies: ["HTML5", "CSS3", "JavaScript", "Local Storage API"],
      image: "/images/projects/recipe-app.jpg",
      github: "https://github.com/jayblacc/recipe-app",
      demo: "https://recipe-app-demo.com",
      featured: true,
      category: "web-app",
      status: "completed"
    },
    {
      id: "health-tracker",
      title: "Health Tracker",
      subtitle: "Health & Wellness",
      description: "A simple web application that allows users to track their breathing patterns and monitor their respiratory health.",
      longDescription: "An innovative health tracking application that helps users monitor their breathing patterns, heart rate, and overall wellness metrics. Features real-time data visualization and personalized health insights.",
      technologies: ["HTML5", "CSS3", "JavaScript", "Webpack", "Chart.js"],
      image: "/images/projects/health-tracker.jpg",
      github: "https://github.com/jayblacc/health-tracker",
      demo: "https://health-tracker-demo.com",
      featured: false,
      category: "web-app",
      status: "completed"
    }
  ],

  // Animation Configuration
  animations: {
    enabled: true,
    duration: 800,
    easing: "ease-out-cubic",
    offset: 50,
    once: true,
    delay: 0
  },

  // SEO Configuration
  seo: {
    title: "Johnson - Web Developer Portfolio",
    description: "Passionate Frontend Developer with 3+ years of experience. Specializing in JavaScript, React, and modern web technologies.",
    keywords: ["web developer", "frontend", "javascript", "react", "portfolio"],
    author: "Johnson",
    image: "/images/og-image.jpg",
    url: "https://johnson-portfolio.com"
  },

  // Feature Flags
  features: {
    darkMode: true,
    animations: true,
    particleBackground: true,
    soundEffects: false,
    analytics: true,
    contactForm: true,
    blog: false,
    testimonials: false
  }
};

// Configuration validation schema
export const CONFIG_SCHEMA = {
  personal: {
    required: ['name', 'title', 'bio'],
    optional: ['subtitle', 'email', 'phone', 'location', 'image', 'resume']
  },
  navigation: {
    required: ['text', 'path', 'section'],
    optional: ['isActive', 'enabled']
  },
  sections: {
    required: ['enabled', 'component', 'order'],
    optional: ['config']
  },
  theme: {
    required: ['colors', 'fonts'],
    optional: ['spacing', 'borderRadius', 'shadows']
  }
};
