/**
 * Template Factory System
 * Creates portfolio components based on configuration
 */

import { createElement, createHtmlElement } from '../utils/utils.js';

/**
 * Base Component Class
 * All portfolio components extend from this base class
 */
export class BaseComponent {
  constructor(config, globalConfig) {
    this.config = config;
    this.globalConfig = globalConfig;
    this.element = null;
    this.eventListeners = [];
  }
  
  /**
   * Render the component
   * @returns {HTMLElement} - The rendered component
   */
  render() {
    throw new Error('render() method must be implemented by subclass');
  }
  
  /**
   * Clean up component resources
   */
  destroy() {
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    this.eventListeners = [];
    
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }
  
  /**
   * Add event listener with cleanup tracking
   * @param {HTMLElement} element - Element to attach listener to
   * @param {string} event - Event type
   * @param {Function} handler - Event handler
   */
  addEventListener(element, event, handler) {
    element.addEventListener(event, handler);
    this.eventListeners.push({ element, event, handler });
  }
  
  /**
   * Apply theme styles to element
   * @param {HTMLElement} element - Element to style
   * @param {Object} styles - Style object
   */
  applyThemeStyles(element, styles) {
    const theme = this.globalConfig.getTheme();
    
    Object.entries(styles).forEach(([property, value]) => {
      // Replace theme variables
      if (typeof value === 'string' && value.startsWith('$')) {
        const themeKey = value.substring(1);
        const [category, key] = themeKey.split('.');
        
        if (theme[category] && theme[category][key]) {
          element.style[property] = theme[category][key];
        }
      } else {
        element.style[property] = value;
      }
    });
  }
}

/**
 * Header Component Factory
 */
export class HeaderComponent extends BaseComponent {
  render() {
    const personal = this.globalConfig.getPersonal();
    const navigation = this.globalConfig.getNavigation();
    const theme = this.globalConfig.getTheme();
    
    const header = createHtmlElement('header', {
      class: 'header',
      'data-aos': 'fade-down',
      role: 'banner'
    });
    
    // Brand
    const brand = createElement('div', {
      class: 'brand',
      role: 'heading',
      'aria-level': '1'
    }, personal.brand || personal.name);
    
    // Navigation
    const nav = this.createNavigation(navigation);
    
    // Mobile menu
    const mobileNav = this.createMobileNavigation(navigation);
    const burgerMenu = this.createBurgerMenu();
    
    // Hire me link (if contact section is enabled)
    const contactSection = this.globalConfig.getSection('sections').contact;
    let hireMeLink = null;
    if (contactSection && contactSection.enabled) {
      hireMeLink = createElement('a', {
        class: 'nav__link',
        href: '/contact'
      }, 'Hire Me');
    }
    
    header.append(mobileNav, brand, nav);
    if (hireMeLink) header.appendChild(hireMeLink);
    header.appendChild(burgerMenu);
    
    this.setupEventListeners(header);
    
    return header;
  }
  
  createNavigation(navigationItems) {
    const nav = createHtmlElement('nav', {
      class: 'nav__menu',
      role: 'navigation',
      'aria-label': 'Main navigation'
    });
    
    navigationItems.forEach(item => {
      const link = createElement('a', {
        class: `btn nav__btn ${item.isActive ? 'active' : ''}`,
        href: item.path,
        role: 'menuitem',
        tabindex: '0',
        'aria-current': item.isActive ? 'page' : 'false'
      }, item.text);
      
      nav.appendChild(link);
    });
    
    return nav;
  }
  
  createMobileNavigation(navigationItems) {
    const nav = createHtmlElement('nav', {
      class: 'mobile__menu nav__menu'
    });
    
    navigationItems.forEach(item => {
      const link = createElement('a', {
        class: `btn nav__btn ${item.isActive ? 'active' : ''}`,
        href: item.path,
        role: 'menuitem',
        tabindex: '0',
        'aria-current': item.isActive ? 'page' : 'false'
      }, item.text);
      
      nav.appendChild(link);
    });
    
    return nav;
  }
  
  createBurgerMenu() {
    const burgerMenu = createElement('button', {
      class: 'burger-menu',
      id: 'burger-menu',
      'aria-label': 'Toggle mobile menu',
      'aria-expanded': 'false'
    }, '☰');
    
    return burgerMenu;
  }
  
  setupEventListeners(header) {
    // Navigation click handlers
    const navLinks = header.querySelectorAll('.nav__btn');
    navLinks.forEach(link => {
      this.addEventListener(link, 'click', (e) => {
        e.preventDefault();
        const path = link.getAttribute('href');
        const sectionName = path === '/' ? 'home' : path.substring(1);
        
        // Dispatch custom event for navigation
        document.dispatchEvent(new CustomEvent('portfolio:navigate', {
          detail: { section: sectionName, path }
        }));
      });
    });
    
    // Burger menu handler
    const burgerMenu = header.querySelector('#burger-menu');
    if (burgerMenu) {
      this.addEventListener(burgerMenu, 'click', () => {
        const mobileMenu = header.querySelector('.mobile__menu');
        const isExpanded = burgerMenu.getAttribute('aria-expanded') === 'true';
        
        burgerMenu.setAttribute('aria-expanded', !isExpanded);
        mobileMenu.classList.toggle('open');
        document.body.classList.toggle('menu-open');
      });
    }
  }
}

/**
 * Footer Component Factory
 */
export class FooterComponent extends BaseComponent {
  render() {
    const personal = this.globalConfig.getPersonal();
    const currentYear = new Date().getFullYear();
    
    const footer = createElement('footer', { class: 'footer' });
    const container = createElement('div', { class: 'center' });
    const paragraph = createElement('p', { 
      class: 'paragraph' 
    }, `Copyright ©️ All rights reserved, made with 🖤 by ${personal.name}😊 ${currentYear}`);
    
    container.appendChild(paragraph);
    footer.appendChild(container);
    
    return footer;
  }
}

/**
 * Section Factory
 * Creates section components based on configuration
 */
export class SectionFactory {
  constructor(globalConfig) {
    this.globalConfig = globalConfig;
    this.components = new Map();
    this.registerDefaultComponents();
  }
  
  /**
   * Register default component types
   */
  registerDefaultComponents() {
    this.components.set('HomeSection', () => import('../sections/home-section.js'));
    this.components.set('AboutSection', () => import('../sections/about-section.js'));
    this.components.set('SkillsSection', () => import('../sections/skills-section.js'));
    this.components.set('ProjectsSection', () => import('../sections/projects-section.js'));
    this.components.set('ContactSection', () => import('../sections/contact-section.js'));
  }
  
  /**
   * Register a custom component
   * @param {string} name - Component name
   * @param {Function} factory - Component factory function
   */
  registerComponent(name, factory) {
    this.components.set(name, factory);
  }
  
  /**
   * Create a section component
   * @param {string} sectionName - Section name
   * @param {Object} sectionConfig - Section configuration
   * @returns {Promise<BaseComponent>} - Component instance
   */
  async createSection(sectionName, sectionConfig) {
    const componentFactory = this.components.get(sectionConfig.component);
    
    if (!componentFactory) {
      console.warn(`Component ${sectionConfig.component} not found for section ${sectionName}`);
      return null;
    }
    
    try {
      const module = await componentFactory();
      const ComponentClass = module.default || module[sectionConfig.component];
      
      if (!ComponentClass) {
        console.warn(`Component class not found in module for ${sectionConfig.component}`);
        return null;
      }
      
      return new ComponentClass(sectionConfig, this.globalConfig);
    } catch (error) {
      console.error(`Failed to create section ${sectionName}:`, error);
      return null;
    }
  }
}

/**
 * Main Template Factory
 * Orchestrates the creation of the entire portfolio
 */
export class TemplateFactory {
  constructor(configManager) {
    this.configManager = configManager;
    this.sectionFactory = new SectionFactory(configManager);
    this.components = [];
  }
  
  /**
   * Create the complete portfolio
   * @returns {Promise<HTMLElement>} - Complete portfolio element
   */
  async createPortfolio() {
    const app = document.getElementById('app');
    if (!app) {
      throw new Error('App container element not found');
    }
    
    // Apply theme CSS
    this.applyThemeCSS();
    
    // Create header
    const headerComponent = new HeaderComponent({}, this.configManager);
    const header = headerComponent.render();
    this.components.push(headerComponent);
    
    // Create main content container
    const content = createElement('div', { 
      id: 'content', 
      class: 'content' 
    });
    
    // Create sections
    const enabledSections = this.configManager.getEnabledSections();
    
    for (const section of enabledSections) {
      const sectionComponent = await this.sectionFactory.createSection(section.name, section);
      if (sectionComponent) {
        const sectionElement = sectionComponent.render();
        content.appendChild(sectionElement);
        this.components.push(sectionComponent);
      }
    }
    
    // Create footer
    const footerComponent = new FooterComponent({}, this.configManager);
    const footer = footerComponent.render();
    this.components.push(footerComponent);
    
    // Assemble portfolio
    app.innerHTML = ''; // Clear existing content
    app.append(header, content, footer);
    
    // Setup global event listeners
    this.setupGlobalEventListeners();
    
    return app;
  }
  
  /**
   * Apply theme CSS to the document
   */
  applyThemeCSS() {
    const themeCSS = this.configManager.generateThemeCSS();
    
    // Remove existing theme styles
    const existingStyle = document.getElementById('portfolio-theme');
    if (existingStyle) {
      existingStyle.remove();
    }
    
    // Add new theme styles
    const style = document.createElement('style');
    style.id = 'portfolio-theme';
    style.textContent = themeCSS;
    document.head.appendChild(style);
  }
  
  /**
   * Setup global event listeners
   */
  setupGlobalEventListeners() {
    // Navigation event listener
    document.addEventListener('portfolio:navigate', (e) => {
      const { section, path } = e.detail;
      this.showSection(section);
      this.setActiveLink(path);
      
      // Update browser history
      history.pushState({ section }, '', path);
    });
    
    // Browser back/forward handling
    window.addEventListener('popstate', (e) => {
      const section = e.state?.section || 'home';
      const path = section === 'home' ? '/' : `/${section}`;
      this.showSection(section);
      this.setActiveLink(path);
    });
  }
  
  /**
   * Show a specific section
   * @param {string} sectionName - Section to show
   */
  showSection(sectionName) {
    const sections = document.querySelectorAll('.hero');
    sections.forEach(section => {
      const shouldShow = section.id === sectionName.toLowerCase();
      section.classList.toggle('active-section', shouldShow);
      section.style.display = shouldShow ? 'flex' : 'none';
    });
  }
  
  /**
   * Set active navigation link
   * @param {string} path - Active path
   */
  setActiveLink(path) {
    const allLinks = document.querySelectorAll('.nav__btn, .nav__link');
    allLinks.forEach(link => {
      const isActive = link.getAttribute('href') === path;
      link.classList.toggle('active', isActive);
      link.setAttribute('aria-current', isActive ? 'page' : 'false');
    });
  }
  
  /**
   * Destroy all components and clean up
   */
  destroy() {
    this.components.forEach(component => component.destroy());
    this.components = [];
    
    // Remove theme styles
    const themeStyle = document.getElementById('portfolio-theme');
    if (themeStyle) {
      themeStyle.remove();
    }
  }
}
