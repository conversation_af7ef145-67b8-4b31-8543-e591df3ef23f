/**
 * Configuration Manager
 * Handles configuration validation, merging, and management
 */

import { DEFAULT_PORTFOLIO_CONFIG, CONFIG_SCHEMA } from './portfolio.config.js';

/**
 * Deep merge two objects, with the second object taking precedence
 * @param {Object} target - The target object
 * @param {Object} source - The source object to merge
 * @returns {Object} - The merged object
 */
const deepMerge = (target, source) => {
  const result = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
  }
  
  return result;
};

/**
 * Validate configuration against schema
 * @param {Object} config - Configuration to validate
 * @param {Object} schema - Schema to validate against
 * @returns {Object} - Validation result with errors array
 */
const validateConfig = (config, schema = CONFIG_SCHEMA) => {
  const errors = [];
  
  // Validate personal information
  if (config.personal) {
    const missing = schema.personal.required.filter(field => !config.personal[field]);
    if (missing.length > 0) {
      errors.push(`Missing required personal fields: ${missing.join(', ')}`);
    }
  } else {
    errors.push('Personal information is required');
  }
  
  // Validate navigation
  if (config.navigation && Array.isArray(config.navigation)) {
    config.navigation.forEach((nav, index) => {
      const missing = schema.navigation.required.filter(field => !nav[field]);
      if (missing.length > 0) {
        errors.push(`Navigation item ${index}: Missing required fields: ${missing.join(', ')}`);
      }
    });
  }
  
  // Validate sections
  if (config.sections) {
    Object.entries(config.sections).forEach(([sectionName, section]) => {
      const missing = schema.sections.required.filter(field => section[field] === undefined);
      if (missing.length > 0) {
        errors.push(`Section ${sectionName}: Missing required fields: ${missing.join(', ')}`);
      }
    });
  }
  
  // Validate theme
  if (config.theme) {
    const missing = schema.theme.required.filter(field => !config.theme[field]);
    if (missing.length > 0) {
      errors.push(`Theme: Missing required fields: ${missing.join(', ')}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Configuration Manager Class
 */
export class ConfigManager {
  constructor(userConfig = {}) {
    this.userConfig = userConfig;
    this.config = this.mergeConfigs(DEFAULT_PORTFOLIO_CONFIG, userConfig);
    this.validation = validateConfig(this.config);
    
    if (!this.validation.isValid) {
      console.warn('Configuration validation failed:', this.validation.errors);
    }
  }
  
  /**
   * Merge user configuration with default configuration
   * @param {Object} defaultConfig - Default configuration
   * @param {Object} userConfig - User configuration
   * @returns {Object} - Merged configuration
   */
  mergeConfigs(defaultConfig, userConfig) {
    return deepMerge(defaultConfig, userConfig);
  }
  
  /**
   * Get the complete merged configuration
   * @returns {Object} - Complete configuration
   */
  getConfig() {
    return this.config;
  }
  
  /**
   * Get a specific section of the configuration
   * @param {string} section - Section name
   * @returns {*} - Section configuration
   */
  getSection(section) {
    return this.config[section];
  }
  
  /**
   * Get personal information
   * @returns {Object} - Personal information
   */
  getPersonal() {
    return this.config.personal;
  }
  
  /**
   * Get navigation configuration
   * @returns {Array} - Navigation items
   */
  getNavigation() {
    return this.config.navigation.filter(nav => nav.enabled !== false);
  }
  
  /**
   * Get enabled sections in order
   * @returns {Array} - Enabled sections sorted by order
   */
  getEnabledSections() {
    return Object.entries(this.config.sections)
      .filter(([_, section]) => section.enabled)
      .sort(([_, a], [__, b]) => a.order - b.order)
      .map(([name, section]) => ({ name, ...section }));
  }
  
  /**
   * Get theme configuration
   * @returns {Object} - Theme configuration
   */
  getTheme() {
    return this.config.theme;
  }
  
  /**
   * Get skills configuration
   * @returns {Array} - Skills array
   */
  getSkills() {
    return this.config.skills || [];
  }
  
  /**
   * Get skills by category
   * @param {string} category - Category name
   * @returns {Array} - Filtered skills
   */
  getSkillsByCategory(category) {
    return this.getSkills().filter(skill => skill.category === category);
  }
  
  /**
   * Get projects configuration
   * @returns {Array} - Projects array
   */
  getProjects() {
    return this.config.projects || [];
  }
  
  /**
   * Get featured projects
   * @returns {Array} - Featured projects
   */
  getFeaturedProjects() {
    return this.getProjects().filter(project => project.featured);
  }
  
  /**
   * Get projects by category
   * @param {string} category - Category name
   * @returns {Array} - Filtered projects
   */
  getProjectsByCategory(category) {
    return this.getProjects().filter(project => project.category === category);
  }
  
  /**
   * Get social media links
   * @returns {Object} - Social media configuration
   */
  getSocial() {
    return this.config.social;
  }
  
  /**
   * Get animation configuration
   * @returns {Object} - Animation settings
   */
  getAnimations() {
    return this.config.animations;
  }
  
  /**
   * Get SEO configuration
   * @returns {Object} - SEO settings
   */
  getSEO() {
    return this.config.seo;
  }
  
  /**
   * Check if a feature is enabled
   * @param {string} feature - Feature name
   * @returns {boolean} - Whether feature is enabled
   */
  isFeatureEnabled(feature) {
    return this.config.features[feature] === true;
  }
  
  /**
   * Get validation result
   * @returns {Object} - Validation result
   */
  getValidation() {
    return this.validation;
  }
  
  /**
   * Update configuration at runtime
   * @param {Object} newConfig - New configuration to merge
   */
  updateConfig(newConfig) {
    this.config = this.mergeConfigs(this.config, newConfig);
    this.validation = validateConfig(this.config);
  }
  
  /**
   * Generate CSS custom properties from theme
   * @returns {string} - CSS custom properties
   */
  generateThemeCSS() {
    const theme = this.getTheme();
    let css = ':root {\n';
    
    // Colors
    Object.entries(theme.colors).forEach(([key, value]) => {
      css += `  --color-${key}: ${value};\n`;
    });
    
    // Fonts
    Object.entries(theme.fonts).forEach(([key, value]) => {
      css += `  --font-${key}: ${value};\n`;
    });
    
    // Spacing
    if (theme.spacing) {
      Object.entries(theme.spacing).forEach(([key, value]) => {
        css += `  --spacing-${key}: ${value};\n`;
      });
    }
    
    // Border radius
    if (theme.borderRadius) {
      css += `  --border-radius: ${theme.borderRadius};\n`;
    }
    
    // Shadows
    if (theme.shadows) {
      Object.entries(theme.shadows).forEach(([key, value]) => {
        css += `  --shadow-${key}: ${value};\n`;
      });
    }
    
    css += '}';
    return css;
  }
}

/**
 * Create a configuration manager instance
 * @param {Object} userConfig - User configuration
 * @returns {ConfigManager} - Configuration manager instance
 */
export const createConfigManager = (userConfig = {}) => {
  return new ConfigManager(userConfig);
};

/**
 * Load configuration from external source (JSON file, API, etc.)
 * @param {string} source - Configuration source
 * @returns {Promise<Object>} - Configuration object
 */
export const loadConfigFromSource = async (source) => {
  try {
    if (source.endsWith('.json')) {
      const response = await fetch(source);
      return await response.json();
    }
    // Add other source types as needed
    return {};
  } catch (error) {
    console.error('Failed to load configuration from source:', error);
    return {};
  }
};
