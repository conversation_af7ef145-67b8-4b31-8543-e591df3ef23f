# Portfolio Template System

A modern, configuration-driven portfolio template built with vanilla JavaScript and functional programming principles.

## 🚀 Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
git clone <your-repo-url>
cd portfolio
npm install
```

### 2. Customize Your Portfolio

Edit `src/config/user.config.js` with your information:

```javascript
export const USER_CONFIG = {
  personal: {
    name: "Your Name",
    title: "Your Title",
    bio: "Your bio here...",
    email: "<EMAIL>",
    image: "/images/your-photo.jpg"
  },
  
  theme: {
    colors: {
      primary: "#your-brand-color"
    }
  }
  
  // ... more configuration
};
```

### 3. Run Development Server

```bash
npm run dev
```

### 4. Build for Production

```bash
npm run build
```

## 🎨 Features

- **Configuration-Driven**: Customize everything through config files
- **Responsive Design**: Works perfectly on all devices
- **Modern Animations**: Smooth AOS animations and interactions
- **Theme System**: Easy color and styling customization
- **SEO Optimized**: Built-in SEO configuration
- **Performance**: Optimized loading and rendering
- **Accessibility**: WCAG compliant components
- **Modular**: Add/remove sections easily

## 📁 Project Structure

```
src/
├── config/
│   ├── portfolio.config.js    # Default configuration
│   ├── user.config.js         # Your customizations
│   └── config-manager.js      # Configuration management
├── core/
│   ├── portfolio-app.js       # Main application
│   └── template-factory.js    # Component factory
├── sections/
│   ├── home-section.js        # Home page component
│   ├── about-section.js       # About section
│   ├── skills-section.js      # Skills section
│   ├── projects-section.js    # Projects showcase
│   └── contact-section.js     # Contact form
├── utils/
│   └── utils.js               # Utility functions
└── style/
    └── *.css                  # Styling files
```

## ⚙️ Configuration Guide

### Personal Information

```javascript
personal: {
  name: "John Doe",
  title: "Full Stack Developer", 
  subtitle: "React • Node.js • TypeScript",
  bio: "Passionate developer with 5+ years experience...",
  email: "<EMAIL>",
  phone: "+****************",
  location: "San Francisco, CA",
  image: "/images/profile.jpg",
  resume: "/assets/resume.pdf"
}
```

### Theme Customization

```javascript
theme: {
  colors: {
    primary: "#6366f1",        // Your brand color
    secondary: "#8b5cf6",      // Secondary color
    background: "#0f172a",     // Dark background
    text: "#f8fafc"            // Text color
  },
  fonts: {
    primary: "'Inter', sans-serif",
    secondary: "'JetBrains Mono', monospace"
  }
}
```

### Skills Configuration

```javascript
skills: [
  {
    name: "JavaScript",
    level: 95,
    icon: "/images/icons/js.svg",
    category: "frontend"
  },
  {
    name: "React",
    level: 90,
    icon: "/images/icons/react.svg", 
    category: "frontend"
  }
  // ... more skills
]
```

### Projects Showcase

```javascript
projects: [
  {
    id: "project-1",
    title: "E-Commerce Platform",
    description: "Full-stack e-commerce solution...",
    technologies: ["React", "Node.js", "MongoDB"],
    image: "/images/projects/ecommerce.jpg",
    github: "https://github.com/you/project",
    demo: "https://project-demo.com",
    featured: true
  }
  // ... more projects
]
```

## 🎯 Customization Examples

### Minimal Portfolio

```javascript
import { MINIMAL_CONFIG } from './src/config/user.config.js';

const app = await createPortfolioApp(MINIMAL_CONFIG);
```

### Designer Portfolio

```javascript
import { DESIGNER_CONFIG } from './src/config/user.config.js';

const app = await createPortfolioApp(DESIGNER_CONFIG);
```

### Custom Configuration

```javascript
const CUSTOM_CONFIG = {
  personal: {
    name: "Jane Designer",
    title: "UI/UX Designer"
  },
  
  sections: {
    home: { enabled: true, order: 1 },
    about: { enabled: true, order: 2 },
    projects: { enabled: true, order: 3 },
    contact: { enabled: true, order: 4 }
  },
  
  theme: {
    colors: {
      primary: "#ff6b6b",
      secondary: "#4ecdc4"
    }
  }
};
```

## 🔧 Advanced Usage

### Adding Custom Sections

1. Create your section component:

```javascript
// src/sections/blog-section.js
import { BaseComponent } from '../core/template-factory.js';

export default class BlogSection extends BaseComponent {
  render() {
    // Your section implementation
  }
}
```

2. Register in configuration:

```javascript
sections: {
  blog: {
    enabled: true,
    component: "BlogSection",
    order: 5
  }
}
```

### Runtime Configuration Updates

```javascript
// Get app instance
const app = window.portfolioApp;

// Update configuration
await app.updateConfig({
  theme: {
    colors: {
      primary: "#new-color"
    }
  }
});
```

## 🎨 Theming

### CSS Custom Properties

The system automatically generates CSS custom properties:

```css
:root {
  --color-primary: #6366f1;
  --color-secondary: #8b5cf6;
  --font-primary: 'Inter', sans-serif;
  --spacing-md: 1rem;
  --border-radius: 12px;
}
```

### Using Theme in Components

```javascript
// Apply theme styles
this.applyThemeStyles(element, {
  backgroundColor: '$colors.primary',
  color: '$colors.text',
  borderRadius: '$borderRadius'
});
```

## 📱 Responsive Design

The template is fully responsive with:
- Mobile-first approach
- Flexible grid system
- Adaptive typography
- Touch-friendly interactions
- Optimized images

## ♿ Accessibility

Built with accessibility in mind:
- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation
- Screen reader support
- High contrast support
- Focus management

## 🚀 Performance

Optimized for performance:
- Lazy loading
- Code splitting
- Image optimization
- Minimal bundle size
- Efficient rendering
- Caching strategies

## 🔍 SEO

SEO-ready features:
- Meta tags configuration
- Open Graph support
- Structured data
- Sitemap generation
- Clean URLs
- Fast loading

## 📊 Analytics

Easy analytics integration:
- Google Analytics support
- Custom event tracking
- Performance monitoring
- User behavior tracking

## 🛠️ Development

### Available Scripts

```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run watch    # Watch for changes
```

### Environment Variables

```bash
NODE_ENV=development  # Enable debug mode
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- 📖 [Documentation](./docs/TEMPLATE_SYSTEM.md)
- 🐛 [Issue Tracker](./issues)
- 💬 [Discussions](./discussions)

## 🎉 Showcase

Built with this template:
- [Example Portfolio 1](https://example1.com)
- [Example Portfolio 2](https://example2.com)
- [Example Portfolio 3](https://example3.com)

---

**Made with ❤️ and modern web technologies**
