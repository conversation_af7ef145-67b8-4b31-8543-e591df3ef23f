/**
 * Portfolio Application
 * Main application class that orchestrates the entire portfolio using the template system
 */

import { createConfigManager } from '../config/config-manager.js';
import { TemplateFactory } from './template-factory.js';
import AOS from 'aos';
import 'aos/dist/aos.css';

/**
 * Portfolio Application Class
 */
export class PortfolioApp {
  constructor(userConfig = {}) {
    this.configManager = createConfigManager(userConfig);
    this.templateFactory = new TemplateFactory(this.configManager);
    this.isInitialized = false;
    this.plugins = new Map();
  }
  
  /**
   * Initialize the portfolio application
   * @returns {Promise<void>}
   */
  async init() {
    try {
      // Validate configuration
      const validation = this.configManager.getValidation();
      if (!validation.isValid) {
        console.error('Configuration validation failed:', validation.errors);
        throw new Error('Invalid configuration');
      }
      
      // Initialize plugins
      await this.initializePlugins();
      
      // Create and render portfolio
      await this.templateFactory.createPortfolio();
      
      // Setup initial state
      this.setupInitialState();
      
      // Mark as initialized
      this.isInitialized = true;
      
      // Dispatch ready event
      document.dispatchEvent(new CustomEvent('portfolio:ready', {
        detail: { app: this }
      }));
      
      console.log('Portfolio application initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize portfolio application:', error);
      throw error;
    }
  }
  
  /**
   * Initialize plugins and libraries
   * @returns {Promise<void>}
   */
  async initializePlugins() {
    const features = this.configManager.getSection('features');
    const animations = this.configManager.getAnimations();
    
    // Initialize AOS (Animate On Scroll) if animations are enabled
    if (features.animations && animations.enabled) {
      AOS.init({
        duration: animations.duration || 800,
        once: animations.once !== false,
        offset: animations.offset || 50,
        easing: animations.easing || 'ease-out-cubic',
        delay: animations.delay || 0
      });
      
      this.plugins.set('aos', AOS);
    }
    
    // Initialize particles.js if enabled
    if (features.particleBackground) {
      try {
        const particlesJS = await import('particles.js');
        // Configure particles based on theme
        const theme = this.configManager.getTheme();
        this.initializeParticles(particlesJS, theme);
        this.plugins.set('particles', particlesJS);
      } catch (error) {
        console.warn('Failed to load particles.js:', error);
      }
    }
    
    // Initialize other plugins as needed
    await this.initializeCustomPlugins();
  }
  
  /**
   * Initialize particles.js with theme-based configuration
   * @param {Object} particlesJS - Particles.js library
   * @param {Object} theme - Theme configuration
   */
  initializeParticles(particlesJS, theme) {
    const particleConfig = {
      particles: {
        number: {
          value: 80,
          density: {
            enable: true,
            value_area: 800
          }
        },
        color: {
          value: theme.colors.primary
        },
        shape: {
          type: 'circle'
        },
        opacity: {
          value: 0.5,
          random: false
        },
        size: {
          value: 3,
          random: true
        },
        line_linked: {
          enable: true,
          distance: 150,
          color: theme.colors.primary,
          opacity: 0.4,
          width: 1
        },
        move: {
          enable: true,
          speed: 6,
          direction: 'none',
          random: false,
          straight: false,
          out_mode: 'out',
          bounce: false
        }
      },
      interactivity: {
        detect_on: 'canvas',
        events: {
          onhover: {
            enable: true,
            mode: 'repulse'
          },
          onclick: {
            enable: true,
            mode: 'push'
          },
          resize: true
        }
      },
      retina_detect: true
    };
    
    // Create particles container if it doesn't exist
    let particlesContainer = document.getElementById('particles-js');
    if (!particlesContainer) {
      particlesContainer = document.createElement('div');
      particlesContainer.id = 'particles-js';
      particlesContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        pointer-events: none;
      `;
      document.body.appendChild(particlesContainer);
    }
    
    particlesJS.default('particles-js', particleConfig);
  }
  
  /**
   * Initialize custom plugins
   * @returns {Promise<void>}
   */
  async initializeCustomPlugins() {
    // Override this method to add custom plugin initialization
    // Example:
    // if (this.configManager.isFeatureEnabled('customPlugin')) {
    //   const plugin = await import('./plugins/custom-plugin.js');
    //   plugin.init(this.configManager);
    //   this.plugins.set('customPlugin', plugin);
    // }
  }
  
  /**
   * Setup initial application state
   */
  setupInitialState() {
    // Set initial section based on URL
    const currentPath = window.location.pathname;
    const currentSection = currentPath === '/' ? 'home' : currentPath.substring(1);
    
    // Validate section exists
    const enabledSections = this.configManager.getEnabledSections();
    const sectionExists = enabledSections.some(section => section.name === currentSection);
    
    if (sectionExists) {
      this.templateFactory.showSection(currentSection);
      this.templateFactory.setActiveLink(currentPath);
    } else {
      // Fallback to home section
      this.templateFactory.showSection('home');
      this.templateFactory.setActiveLink('/');
      history.replaceState({ section: 'home' }, '', '/');
    }
    
    // Setup progress bars if about section is enabled
    const aboutSection = this.configManager.getSection('sections').about;
    if (aboutSection && aboutSection.enabled) {
      // Delay to ensure DOM is ready
      setTimeout(() => {
        this.initializeProgressBars();
      }, 100);
    }
  }
  
  /**
   * Initialize progress bars for skills
   */
  initializeProgressBars() {
    const progressContainers = document.querySelectorAll('.card__progress-container');
    
    progressContainers.forEach(container => {
      const parent = container.parentNode;
      const initialWidth = container.getAttribute('data-initial-width') || '0';
      
      const animateProgress = (width) => {
        container.style.width = width + '%';
      };
      
      if (parent) {
        parent.addEventListener('mouseenter', () => {
          animateProgress(initialWidth);
        });
        
        parent.addEventListener('mouseleave', () => {
          animateProgress(0);
        });
      }
    });
  }
  
  /**
   * Update configuration at runtime
   * @param {Object} newConfig - New configuration to merge
   * @returns {Promise<void>}
   */
  async updateConfig(newConfig) {
    this.configManager.updateConfig(newConfig);
    
    // Re-initialize if already initialized
    if (this.isInitialized) {
      await this.reinitialize();
    }
  }
  
  /**
   * Reinitialize the application with updated configuration
   * @returns {Promise<void>}
   */
  async reinitialize() {
    // Destroy existing components
    this.templateFactory.destroy();
    
    // Refresh plugins if needed
    await this.refreshPlugins();
    
    // Recreate portfolio
    await this.templateFactory.createPortfolio();
    
    // Setup state again
    this.setupInitialState();
    
    // Dispatch update event
    document.dispatchEvent(new CustomEvent('portfolio:updated', {
      detail: { app: this }
    }));
  }
  
  /**
   * Refresh plugins based on updated configuration
   * @returns {Promise<void>}
   */
  async refreshPlugins() {
    const features = this.configManager.getSection('features');
    
    // Refresh AOS if animations setting changed
    if (features.animations && this.plugins.has('aos')) {
      const animations = this.configManager.getAnimations();
      AOS.refresh();
      AOS.init({
        duration: animations.duration || 800,
        once: animations.once !== false,
        offset: animations.offset || 50,
        easing: animations.easing || 'ease-out-cubic',
        delay: animations.delay || 0
      });
    }
    
    // Refresh particles if theme changed
    if (features.particleBackground && this.plugins.has('particles')) {
      const theme = this.configManager.getTheme();
      this.initializeParticles(this.plugins.get('particles'), theme);
    }
  }
  
  /**
   * Get configuration manager
   * @returns {ConfigManager} - Configuration manager instance
   */
  getConfigManager() {
    return this.configManager;
  }
  
  /**
   * Get template factory
   * @returns {TemplateFactory} - Template factory instance
   */
  getTemplateFactory() {
    return this.templateFactory;
  }
  
  /**
   * Check if application is initialized
   * @returns {boolean} - Whether app is initialized
   */
  isReady() {
    return this.isInitialized;
  }
  
  /**
   * Destroy the application and clean up resources
   */
  destroy() {
    // Destroy template factory
    this.templateFactory.destroy();
    
    // Clean up plugins
    this.plugins.forEach((plugin, name) => {
      if (plugin.destroy && typeof plugin.destroy === 'function') {
        plugin.destroy();
      }
    });
    this.plugins.clear();
    
    // Remove particles container
    const particlesContainer = document.getElementById('particles-js');
    if (particlesContainer) {
      particlesContainer.remove();
    }
    
    // Mark as not initialized
    this.isInitialized = false;
    
    // Dispatch destroy event
    document.dispatchEvent(new CustomEvent('portfolio:destroyed'));
  }
}

/**
 * Create and initialize a portfolio application
 * @param {Object} userConfig - User configuration
 * @returns {Promise<PortfolioApp>} - Initialized portfolio application
 */
export const createPortfolioApp = async (userConfig = {}) => {
  const app = new PortfolioApp(userConfig);
  await app.init();
  return app;
};

/**
 * Global portfolio app instance
 */
let globalPortfolioApp = null;

/**
 * Get or create global portfolio app instance
 * @param {Object} userConfig - User configuration
 * @returns {Promise<PortfolioApp>} - Portfolio application instance
 */
export const getPortfolioApp = async (userConfig = {}) => {
  if (!globalPortfolioApp) {
    globalPortfolioApp = await createPortfolioApp(userConfig);
  }
  return globalPortfolioApp;
};
